import 'package:flutter/material.dart';
import '../../data/models/profile_model.dart';

enum SwipeDirection { left, right, up }

class SwipeCard extends StatefulWidget {
  final ProfileModel profile;
  final Function(SwipeDirection) onSwipe;

  const SwipeCard({
    super.key,
    required this.profile,
    required this.onSwipe,
  });

  @override
  State<SwipeCard> createState() => _SwipeCardState();
}

class _SwipeCardState extends State<SwipeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _offsetAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  Offset _dragOffset = Offset.zero;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _offsetAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  void _onPanStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
    });
  }

  void _onPanUpdate(DragUpdateDetails details) {
    setState(() {
      _dragOffset += details.delta;
    });
  }

  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _isDragging = false;
    });

    final screenWidth = MediaQuery.of(context).size.width;
    final threshold = screenWidth * 0.3;

    // Determine swipe direction
    if (_dragOffset.dx.abs() > threshold || _dragOffset.dy.abs() > threshold) {
      SwipeDirection direction;

      if (_dragOffset.dy < -threshold) {
        direction = SwipeDirection.up; // Super like
      } else if (_dragOffset.dx > threshold) {
        direction = SwipeDirection.right; // Like
      } else {
        direction = SwipeDirection.left; // Pass
      }

      _animateSwipeOut(direction);
    } else {
      _animateBack();
    }
  }

  void _animateSwipeOut(SwipeDirection direction) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    Offset endOffset;
    double endRotation;

    switch (direction) {
      case SwipeDirection.left:
        endOffset = Offset(-screenWidth * 1.5, _dragOffset.dy);
        endRotation = -0.5;
        break;
      case SwipeDirection.right:
        endOffset = Offset(screenWidth * 1.5, _dragOffset.dy);
        endRotation = 0.5;
        break;
      case SwipeDirection.up:
        endOffset = Offset(_dragOffset.dx, -screenHeight * 1.5);
        endRotation = _dragOffset.dx / screenWidth * 0.3;
        break;
    }

    _offsetAnimation = Tween<Offset>(
      begin: _dragOffset,
      end: endOffset,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: _dragOffset.dx / screenWidth * 0.3,
      end: endRotation,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.forward().then((_) {
      widget.onSwipe(direction);
      _resetCard();
    });
  }

  void _animateBack() {
    _offsetAnimation = Tween<Offset>(
      begin: _dragOffset,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: _dragOffset.dx / MediaQuery.of(context).size.width * 0.3,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _controller.forward().then((_) {
      _resetCard();
    });
  }

  void _resetCard() {
    _controller.reset();
    setState(() {
      _dragOffset = Offset.zero;
    });
  }

  Color _getOverlayColor() {
    final screenWidth = MediaQuery.of(context).size.width;
    final threshold = screenWidth * 0.2;

    if (_dragOffset.dy < -threshold) {
      return Colors.blue.withValues(alpha: 0.7); // Super like
    } else if (_dragOffset.dx > threshold) {
      return Colors.green.withValues(alpha: 0.7); // Like
    } else if (_dragOffset.dx < -threshold) {
      return Colors.red.withValues(alpha: 0.7); // Pass
    }

    return Colors.transparent;
  }

  String _getOverlayText() {
    final screenWidth = MediaQuery.of(context).size.width;
    final threshold = screenWidth * 0.2;

    if (_dragOffset.dy < -threshold) {
      return 'SUPER LIKE';
    } else if (_dragOffset.dx > threshold) {
      return 'LIKE';
    } else if (_dragOffset.dx < -threshold) {
      return 'PASS';
    }

    return '';
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanStart: _onPanStart,
      onPanUpdate: _onPanUpdate,
      onPanEnd: _onPanEnd,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          final offset =
              _controller.isAnimating ? _offsetAnimation.value : _dragOffset;
          final rotation = _controller.isAnimating
              ? _rotationAnimation.value
              : _dragOffset.dx / MediaQuery.of(context).size.width * 0.3;
          final scale = _controller.isAnimating ? _scaleAnimation.value : 1.0;

          return Transform.translate(
            offset: offset,
            child: Transform.rotate(
              angle: rotation,
              child: Transform.scale(
                scale: scale,
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: Stack(
                      children: [
                        _buildProfileImage(),
                        _buildGradientOverlay(),
                        _buildProfileInfo(),
                        _buildSwipeOverlay(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileImage() {
    return Positioned.fill(
      child: widget.profile.profileImages.isNotEmpty
          ? Image.network(
              widget.profile.profileImages.first,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) =>
                  _buildPlaceholderImage(),
            )
          : _buildPlaceholderImage(),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      color: Colors.grey[300],
      child: const Center(
        child: Icon(
          Icons.person,
          size: 100,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildGradientOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.transparent,
              Colors.black.withValues(alpha: 0.7),
            ],
            stops: const [0.0, 0.6, 1.0],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileInfo() {
    return Positioned(
      bottom: 20,
      left: 20,
      right: 20,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                widget.profile.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 8),
              if (widget.profile.age != null)
                Text(
                  '${widget.profile.age}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                  ),
                ),
            ],
          ),
          // Location
          if (widget.profile.location != null &&
              widget.profile.location!.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              widget.profile.location!,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
            ),
          ],
          if (widget.profile.bio != null && widget.profile.bio!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              widget.profile.bio!,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          if (widget.profile.location != null &&
              widget.profile.location!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(
                  Icons.location_on,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  widget.profile.location!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
          if (widget.profile.interests.isNotEmpty) ...[
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: widget.profile.interests.take(3).map((interest) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                    border:
                        Border.all(color: Colors.white.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    interest,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSwipeOverlay() {
    final overlayColor = _getOverlayColor();
    final overlayText = _getOverlayText();

    if (overlayColor == Colors.transparent) {
      return const SizedBox.shrink();
    }

    return Positioned.fill(
      child: Container(
        color: overlayColor,
        child: Center(
          child: Text(
            overlayText,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 48,
              fontWeight: FontWeight.bold,
              letterSpacing: 4,
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
