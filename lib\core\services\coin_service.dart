import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'device_quick_login_service.dart';
import 'api_auth_service.dart';

class CoinService {
  static const String _coinBalanceKey = 'coin_balance';
  static const String _transactionHistoryKey = 'transaction_history';
  static const String _backendUrl = 'http://localhost:3000';

  // Backend sync flag
  static const String _syncEnabledKey = 'coin_sync_enabled';

  // Check if backend sync is enabled
  Future<bool> isBackendSyncEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_syncEnabledKey) ??
        false; // Default to false for mobile development
  }

  // Enable/disable backend sync
  Future<void> setBackendSync(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_syncEnabledKey, enabled);
  }

  // Get current coin balance
  Future<int> getCoinBalance() async {
    final prefs = await SharedPreferences.getInstance();
    int balance = prefs.getInt(_coinBalanceKey) ?? 0;

    // For quick login users, sync with device storage if balance is 0
    if (balance == 0) {
      final currentUser = ApiAuthService.currentUser;
      if (currentUser != null && currentUser.email.contains('quick_')) {
        balance = await DeviceQuickLoginService.getStoredCoinBalance();
        if (balance > 0) {
          await prefs.setInt(_coinBalanceKey, balance);
        }
      }
    }

    return balance;
  }

  // Set coin balance
  Future<void> setCoinBalance(int balance) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_coinBalanceKey, balance);

    // Sync with quick login device storage and API
    await DeviceQuickLoginService.syncCoins(balance);
    await ApiAuthService.updateQuickLoginCoins(balance);
  }

  // Add coins to balance
  Future<bool> addCoins(int amount, String type, String description) async {
    try {
      final currentBalance = await getCoinBalance();
      final newBalance = currentBalance + amount;

      await setCoinBalance(newBalance);
      await _addTransaction(amount, type, description, newBalance);

      return true;
    } catch (e) {
      return false;
    }
  }

  // Spend coins from balance
  Future<bool> spendCoins(int amount, String type, String description) async {
    try {
      final currentBalance = await getCoinBalance();

      if (currentBalance < amount) {
        return false; // Insufficient balance
      }

      final newBalance = currentBalance - amount;
      await setCoinBalance(newBalance);
      await _addTransaction(-amount, type, description, newBalance);

      return true;
    } catch (e) {
      return false;
    }
  }

  // Check if user has enough coins
  Future<bool> hasEnoughCoins(int amount) async {
    final balance = await getCoinBalance();
    return balance >= amount;
  }

  // Add transaction to history
  Future<void> _addTransaction(
    int amount,
    String type,
    String description,
    int balanceAfter,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getString(_transactionHistoryKey) ?? '[]';
    final List<dynamic> history = json.decode(historyJson);

    final transaction = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'amount': amount,
      'type': type,
      'description': description,
      'balanceAfter': balanceAfter,
      'timestamp': DateTime.now().toIso8601String(),
    };

    history.insert(0, transaction); // Add to beginning

    // Keep only last 100 transactions
    if (history.length > 100) {
      history.removeRange(100, history.length);
    }

    await prefs.setString(_transactionHistoryKey, json.encode(history));
  }

  // Get transaction history
  Future<List<CoinTransaction>> getTransactionHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getString(_transactionHistoryKey) ?? '[]';
    final List<dynamic> history = json.decode(historyJson);

    return history.map((json) => CoinTransaction.fromJson(json)).toList();
  }

  // Clear transaction history
  Future<void> clearTransactionHistory() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_transactionHistoryKey);
  }

  // Get spending analytics
  Future<Map<String, int>> getSpendingAnalytics() async {
    final transactions = await getTransactionHistory();
    final analytics = <String, int>{};

    for (final transaction in transactions) {
      if (transaction.amount < 0) {
        // Only spending transactions
        final type = transaction.type;
        analytics[type] = (analytics[type] ?? 0) + transaction.amount.abs();
      }
    }

    return analytics;
  }

  // Predefined coin costs for features
  static const Map<String, int> featureCosts = {
    'super_like': 1,
    'boost': 5,
    'rewind': 1,
    'premium_filter': 2,
    'read_receipt': 1,
    'unlimited_likes': 10,
  };

  // Use feature with coin cost
  Future<bool> useFeature(String featureType) async {
    final cost = featureCosts[featureType];
    if (cost == null) return false;

    final hasEnough = await hasEnoughCoins(cost);
    if (!hasEnough) return false;

    return await spendCoins(
      cost,
      featureType,
      _getFeatureDescription(featureType),
    );
  }

  String _getFeatureDescription(String featureType) {
    switch (featureType) {
      case 'super_like':
        return 'Super Like sent';
      case 'boost':
        return 'Profile boosted for 30 minutes';
      case 'rewind':
        return 'Last swipe undone';
      case 'premium_filter':
        return 'Premium filter used';
      case 'read_receipt':
        return 'Read receipt enabled';
      case 'unlimited_likes':
        return 'Unlimited likes for 24 hours';
      default:
        return 'Feature used';
    }
  }

  // Initialize with default coins for new users
  Future<void> initializeNewUser() async {
    final currentBalance = await getCoinBalance();
    if (currentBalance == 0) {
      await addCoins(50, 'welcome_bonus', 'Welcome bonus for new users');
    }
  }

  // Backend Integration Methods

  // Get coin packages from backend
  Future<List<Map<String, dynamic>>> getCoinPackages() async {
    try {
      if (!await isBackendSyncEnabled()) {
        return _getDefaultPackages();
      }

      final response = await http.get(
        Uri.parse('$_backendUrl/coins/packages'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(data['packages']);
        }
      }

      // Fallback to default packages
      return _getDefaultPackages();
    } catch (e) {
      print('Error fetching coin packages: $e');
      return _getDefaultPackages();
    }
  }

  // Default coin packages with INR pricing
  List<Map<String, dynamic>> _getDefaultPackages() {
    return [
      {
        'id': 'starter',
        'coins': 100,
        'bonus_coins': 0,
        'price_inr': "79.00",
        'price_usd': "0.99",
        'is_popular': false,
        'description': 'Perfect for trying out features',
      },
      {
        'id': 'popular',
        'coins': 500,
        'bonus_coins': 50,
        'price_inr': "399.00",
        'price_usd': "4.99",
        'is_popular': true,
        'description': 'Most popular choice',
      },
      {
        'id': 'value',
        'coins': 1000,
        'bonus_coins': 150,
        'price_inr': "799.00",
        'price_usd': "9.99",
        'is_popular': false,
        'description': 'Great value for money',
      },
      {
        'id': 'premium',
        'coins': 2500,
        'bonus_coins': 500,
        'price_inr': "1599.00",
        'price_usd': "19.99",
        'is_popular': false,
        'description': 'Maximum coins and bonus',
      },
    ];
  }

  // Purchase coins with backend integration
  Future<Map<String, dynamic>?> purchaseCoins({
    required String userId,
    required String packageId,
    required String paymentMethod,
    String? paymentId,
  }) async {
    try {
      // Get package details first
      final packages = await getCoinPackages();
      final package = packages.firstWhere(
        (p) => p['id'] == packageId,
        orElse: () => {},
      );

      if (package.isEmpty) {
        throw Exception('Package not found');
      }

      final totalCoins =
          (package['coins'] as int) + (package['bonus_coins'] as int);

      // Try backend purchase if sync is enabled
      if (await isBackendSyncEnabled()) {
        try {
          final response = await http.post(
            Uri.parse('$_backendUrl/coins/purchase'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode({
              'userId': userId,
              'packageId': packageId,
              'paymentMethod': paymentMethod,
              'paymentId':
                  paymentId ?? 'demo_${DateTime.now().millisecondsSinceEpoch}',
            }),
          );

          if (response.statusCode == 200) {
            final data = json.decode(response.body);
            if (data['success'] == true) {
              // Update local balance
              await addCoins(
                  totalCoins, 'purchase', 'Purchased $packageId package');
              return data['transaction'];
            }
          }
        } catch (e) {
          print('Backend purchase failed: $e');
        }
      }

      // Fallback to local purchase
      await addCoins(totalCoins, 'purchase', 'Purchased $packageId package');

      return {
        'coins_purchased': package['coins'],
        'bonus_coins': package['bonus_coins'],
        'total_coins': totalCoins,
        'new_balance': await getCoinBalance(),
        'amount_paid': package['price_inr'],
        'currency': 'INR',
      };
    } catch (e) {
      print('Purchase error: $e');
      return null;
    }
  }

  // Sync coin balance with backend
  Future<bool> syncBalanceWithBackend(String userId) async {
    try {
      if (!await isBackendSyncEnabled()) return false;

      final response = await http.get(
        Uri.parse('$_backendUrl/coins/balance/$userId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          await setCoinBalance(data['balance']);
          return true;
        }
      }
      return false;
    } catch (e) {
      print('Balance sync error: $e');
      return false;
    }
  }

  // Spend coins with backend sync
  Future<bool> spendCoinsWithSync({
    required String userId,
    required int amount,
    required String type,
    required String description,
  }) async {
    try {
      // Check local balance first
      final hasEnough = await hasEnoughCoins(amount);
      if (!hasEnough) return false;

      // Try backend spending if sync is enabled
      if (await isBackendSyncEnabled()) {
        try {
          final response = await http.post(
            Uri.parse('$_backendUrl/coins/spend'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode({
              'userId': userId,
              'amount': amount,
              'type': type,
              'description': description,
            }),
          );

          if (response.statusCode == 200) {
            final data = json.decode(response.body);
            if (data['success'] == true) {
              // Update local balance
              await spendCoins(amount, type, description);
              return true;
            }
          }
        } catch (e) {
          print('Backend spend failed: $e');
        }
      }

      // Fallback to local spending
      return await spendCoins(amount, type, description);
    } catch (e) {
      print('Spend error: $e');
      return false;
    }
  }

  // Get transaction history from backend
  Future<List<CoinTransaction>> getBackendTransactionHistory(
      String userId) async {
    try {
      if (!await isBackendSyncEnabled()) {
        return await getTransactionHistory();
      }

      final response = await http.get(
        Uri.parse('$_backendUrl/coins/transactions/$userId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final transactions = <CoinTransaction>[];
          for (final tx in data['transactions']) {
            transactions.add(CoinTransaction.fromBackendJson(tx));
          }
          return transactions;
        }
      }

      // Fallback to local history
      return await getTransactionHistory();
    } catch (e) {
      print('Transaction history error: $e');
      return await getTransactionHistory();
    }
  }
}

class CoinTransaction {
  final String id;
  final int amount;
  final String type;
  final String description;
  final int balanceAfter;
  final DateTime timestamp;

  CoinTransaction({
    required this.id,
    required this.amount,
    required this.type,
    required this.description,
    required this.balanceAfter,
    required this.timestamp,
  });

  factory CoinTransaction.fromJson(Map<String, dynamic> json) {
    return CoinTransaction(
      id: json['id'],
      amount: json['amount'],
      type: json['type'],
      description: json['description'],
      balanceAfter: json['balanceAfter'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  factory CoinTransaction.fromBackendJson(Map<String, dynamic> json) {
    return CoinTransaction(
      id: json['id'],
      amount: json['amount'],
      type: json['transaction_type'],
      description: json['description'] ?? '',
      balanceAfter: 0, // Backend doesn't store balance after
      timestamp: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'type': type,
      'description': description,
      'balanceAfter': balanceAfter,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  bool get isIncome => amount > 0;
  bool get isExpense => amount < 0;

  String get formattedAmount {
    if (isIncome) {
      return '+$amount';
    } else {
      return '$amount';
    }
  }

  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}
