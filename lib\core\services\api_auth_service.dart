import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class ApiAuthService {
  static const String baseUrl = 'http://localhost:3000';
  static const bool _useMockData = true; // Use mock data for mobile development
  static final StreamController<ApiUser?> _authStateController =
      StreamController<ApiUser?>.broadcast();

  static ApiUser? _currentUser;
  static const String _userKey = 'current_user_api';

  // Getters
  static ApiUser? get currentUser => _currentUser;
  static Stream<ApiUser?> get authStateChanges => _authStateController.stream;

  // Initialize auth service
  static Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString(_userKey);

      if (userData != null) {
        final userMap = jsonDecode(userData);
        _currentUser = ApiUser.fromMap(userMap);

        // Verify user still exists in database
        final fullUserData = await getUserData(_currentUser!.id);
        if (fullUserData != null) {
          _currentUser = ApiUser.fromMap(fullUserData);
        } else {
          _currentUser = null;
          await prefs.remove(_userKey);
        }
      }

      // Emit initial state
      _authStateController.add(_currentUser);
    } catch (e) {
      print('Error initializing API auth: $e');
      _authStateController.add(null);
    }
  }

  // Sign up with email and password
  static Future<ApiUserCredential?> signUpWithEmail({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      if (_useMockData) {
        // Mock signup for mobile
        await Future.delayed(
            const Duration(seconds: 1)); // Simulate network delay

        final mockUser = ApiUser(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          email: email,
          name: name,
          age: 25,
          gender: 'Female',
          coins: 50,
          isPremium: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        _currentUser = mockUser;
        await _saveCurrentUser(_currentUser!);

        // Save mock user data for future logins
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(
            'mock_user_$email', jsonEncode(_currentUser!.toMap()));

        _authStateController.add(_currentUser);
        return ApiUserCredential(user: _currentUser, isNewUser: true);
      }

      final response = await http.post(
        Uri.parse('$baseUrl/auth/signup'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'password': password,
          'name': name,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 201 && data['success'] == true) {
        // Get full user data
        final fullUserData = await getUserData(data['user']['id']);
        if (fullUserData != null) {
          _currentUser = ApiUser.fromMap(fullUserData);
          await _saveCurrentUser(_currentUser!);
          _authStateController.add(_currentUser);
          return ApiUserCredential(user: _currentUser, isNewUser: true);
        }
      } else {
        throw Exception(data['error'] ?? 'Sign up failed');
      }

      return null;
    } catch (e) {
      if (_useMockData) {
        throw Exception('Mock signup failed: ${e.toString()}');
      }
      throw Exception('Sign up failed: ${e.toString()}');
    }
  }

  // Quick Login - Device-specific with random name generation
  static Future<ApiUserCredential?> quickLogin({
    String? name, // Optional - will generate random if not provided
    String? gender, // Optional - will use default if not provided
    int? age, // Optional - will use default if not provided
  }) async {
    try {
      if (_useMockData) {
        // Get or create device-specific quick login account
        final deviceAccount =
            await DeviceQuickLoginService.getOrCreateQuickLoginAccount();

        // Use provided values or defaults from device account
        final finalName = name ?? deviceAccount['name'];
        final finalGender = gender ?? deviceAccount['gender'];
        final finalAge = age ?? deviceAccount['age'];
        final storedCoins =
            await DeviceQuickLoginService.getStoredCoinBalance();

        // Simulate network delay
        await Future.delayed(const Duration(seconds: 1));

        final mockUser = ApiUser(
          id: deviceAccount['id'],
          email: deviceAccount['email'],
          name: finalName,
          age: finalAge,
          gender: finalGender,
          coins: storedCoins,
          isPremium: false,
          createdAt: DateTime.parse(deviceAccount['created_at']),
          updatedAt: DateTime.now(),
        );

        _currentUser = mockUser;
        await _saveCurrentUser(_currentUser!);

        // Update device account with any new values
        if (name != null || gender != null || age != null) {
          final updatedAccount = Map<String, dynamic>.from(deviceAccount);
          if (name != null) updatedAccount['name'] = name;
          if (gender != null) updatedAccount['gender'] = gender;
          if (age != null) updatedAccount['age'] = age;
          await DeviceQuickLoginService.saveQuickLoginAccount(updatedAccount);
        }

        _authStateController.add(_currentUser);
        return ApiUserCredential(user: _currentUser, isNewUser: false);
      }

      // Generate a unique email for quick login users
      final quickEmail =
          'quick_${DateTime.now().millisecondsSinceEpoch}@friendy.app';
      final quickPassword = 'quick_${DateTime.now().millisecondsSinceEpoch}';

      final response = await http.post(
        Uri.parse('$baseUrl/auth/signup'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': quickEmail,
          'password': quickPassword,
          'name': name,
          'age': age ?? 25, // Use provided age or default
          'gender': gender,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 201 && data['success'] == true) {
        // Get full user data
        final fullUserData = await getUserData(data['user']['id']);
        if (fullUserData != null) {
          _currentUser = ApiUser.fromMap(fullUserData);
          await _saveCurrentUser(_currentUser!);
          _authStateController.add(_currentUser);
          return ApiUserCredential(user: _currentUser, isNewUser: true);
        }
      } else {
        throw Exception(data['error'] ?? 'Quick login failed');
      }

      return null;
    } catch (e) {
      if (_useMockData) {
        throw Exception('Mock quick login failed: ${e.toString()}');
      }
      throw Exception('Quick login failed: ${e.toString()}');
    }
  }

  // Sign up with detailed information
  static Future<ApiUserCredential?> signUpWithDetails({
    required String email,
    required String password,
    required String name,
    int? age,
    String? gender,
  }) async {
    try {
      if (_useMockData) {
        // Mock signup for testing
        await Future.delayed(
            const Duration(seconds: 1)); // Simulate network delay

        final mockUser = ApiUser(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          email: email,
          name: name,
          age: age,
          gender: gender,
          coins: 50,
          isPremium: false,
          role: 'customer',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        _currentUser = mockUser;
        await _saveCurrentUser(_currentUser!);

        // Save mock user data for future logins
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(
            'mock_user_$email', jsonEncode(_currentUser!.toMap()));

        _authStateController.add(_currentUser);
        return ApiUserCredential(user: _currentUser, isNewUser: true);
      }

      final response = await http.post(
        Uri.parse('$baseUrl/auth/signup'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'password': password,
          'name': name,
          'age': age,
          'gender': gender,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 201 && data['success'] == true) {
        // Get full user data
        final fullUserData = await getUserData(data['user']['id']);
        if (fullUserData != null) {
          _currentUser = ApiUser.fromMap(fullUserData);
          await _saveCurrentUser(_currentUser!);
          _authStateController.add(_currentUser);
          return ApiUserCredential(user: _currentUser, isNewUser: true);
        }
      } else {
        throw Exception(data['error'] ?? 'Sign up failed');
      }

      return null;
    } catch (e) {
      if (_useMockData) {
        throw Exception('Mock signup failed: ${e.toString()}');
      }
      throw Exception('Sign up failed: ${e.toString()}');
    }
  }

  // Sign in with email and password
  static Future<ApiUserCredential?> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      if (_useMockData) {
        // Mock login for testing
        await Future.delayed(
            const Duration(seconds: 1)); // Simulate network delay

        // Check if user exists in local storage
        final prefs = await SharedPreferences.getInstance();
        final savedUserData = prefs.getString('mock_user_$email');

        if (savedUserData != null) {
          final userData = jsonDecode(savedUserData);
          _currentUser = ApiUser.fromMap(userData);
          await _saveCurrentUser(_currentUser!);
          _authStateController.add(_currentUser);
          return ApiUserCredential(user: _currentUser);
        } else {
          // Create a demo user for testing
          final mockUser = ApiUser(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            email: email,
            name: 'Demo User',
            age: 25,
            gender: 'Female',
            coins: 100,
            isPremium: false,
            role: email.contains('host') ? 'host' : 'customer',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          _currentUser = mockUser;
          await _saveCurrentUser(_currentUser!);
          _authStateController.add(_currentUser);
          return ApiUserCredential(user: _currentUser);
        }
      }

      final response = await http.post(
        Uri.parse('$baseUrl/auth/signin'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        // Get full user data
        final fullUserData = await getUserData(data['user']['id']);
        if (fullUserData != null) {
          _currentUser = ApiUser.fromMap(fullUserData);
          await _saveCurrentUser(_currentUser!);
          _authStateController.add(_currentUser);
          return ApiUserCredential(user: _currentUser);
        }
      } else {
        throw Exception(data['error'] ?? 'Sign in failed');
      }

      return null;
    } catch (e) {
      if (_useMockData) {
        throw Exception('Mock login failed: ${e.toString()}');
      }
      throw Exception('Sign in failed: ${e.toString()}');
    }
  }

  // Sign out
  static Future<void> signOut() async {
    try {
      _currentUser = null;

      // Clear current user from storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);

      // Notify listeners
      _authStateController.add(null);
    } catch (e) {
      throw Exception('Sign out failed: ${e.toString()}');
    }
  }

  // Update coin balance for quick login users
  static Future<void> updateQuickLoginCoins(int newCoinBalance) async {
    if (_currentUser != null && _currentUser!.email.contains('quick_')) {
      // Update device-specific coin storage
      await DeviceQuickLoginService.syncCoins(newCoinBalance);

      // Update current user object
      _currentUser = ApiUser(
        id: _currentUser!.id,
        email: _currentUser!.email,
        name: _currentUser!.name,
        age: _currentUser!.age,
        bio: _currentUser!.bio,
        location: _currentUser!.location,
        profileImages: _currentUser!.profileImages,
        interests: _currentUser!.interests,
        gender: _currentUser!.gender,
        lookingFor: _currentUser!.lookingFor,
        coins: newCoinBalance,
        isPremium: _currentUser!.isPremium,
        role: _currentUser!.role,
        createdAt: _currentUser!.createdAt,
        updatedAt: DateTime.now(),
      );

      await _saveCurrentUser(_currentUser!);
      _authStateController.add(_currentUser);
    }
  }

  // Reset password (placeholder)
  static Future<void> resetPassword(String email) async {
    try {
      // In a real implementation, this would call the backend
      await Future.delayed(const Duration(seconds: 1));

      // For now, just simulate success
      // The backend would need a password reset endpoint
    } catch (e) {
      throw Exception('Password reset failed: ${e.toString()}');
    }
  }

  // Get user data from API
  static Future<Map<String, dynamic>?> getUserData(String userId) async {
    try {
      if (_useMockData) {
        // For mock data, return the current user's data if it matches the userId
        if (_currentUser != null && _currentUser!.id == userId) {
          return _currentUser!.toMap();
        }

        // If no current user or ID doesn't match, check saved users
        final prefs = await SharedPreferences.getInstance();
        final savedUserData = prefs.getString(_userKey);
        if (savedUserData != null) {
          final userData = jsonDecode(savedUserData);
          if (userData['id'] == userId) {
            return userData;
          }
        }

        // Check for mock users by email pattern
        final mockUserKeys =
            prefs.getKeys().where((key) => key.startsWith('mock_user_'));
        for (final key in mockUserKeys) {
          final userData = prefs.getString(key);
          if (userData != null) {
            final userMap = jsonDecode(userData);
            if (userMap['id'] == userId) {
              return userMap;
            }
          }
        }

        return null;
      }

      final response = await http.get(
        Uri.parse('$baseUrl/users/$userId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return data['user'];
        }
      }

      return null;
    } catch (e) {
      print('Error getting user data: $e');
      return null;
    }
  }

  // Update user profile
  static Future<bool> updateUserProfile({
    required String userId,
    String? name,
    int? age,
    String? bio,
    String? location,
    List<String>? interests,
    String? gender,
    String? lookingFor,
  }) async {
    try {
      final updateData = <String, dynamic>{};

      if (name != null) updateData['name'] = name;
      if (age != null) updateData['age'] = age;
      if (bio != null) updateData['bio'] = bio;
      if (location != null) updateData['location'] = location;
      if (interests != null) updateData['interests'] = interests;
      if (gender != null) updateData['gender'] = gender;
      if (lookingFor != null) updateData['looking_for'] = lookingFor;

      final response = await http.put(
        Uri.parse('$baseUrl/users/$userId'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(updateData),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['success'] == true;
      }

      return false;
    } catch (e) {
      print('Error updating user profile: $e');
      return false;
    }
  }

  // Delete account (placeholder)
  static Future<void> deleteAccount() async {
    if (_currentUser == null) return;

    try {
      // In a real implementation, this would call the backend
      // For now, just clear the session
      await signOut();
    } catch (e) {
      throw Exception('Failed to delete account: ${e.toString()}');
    }
  }

  // Check API health
  static Future<bool> checkApiHealth() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/health'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));

      return response.statusCode == 200;
    } catch (e) {
      print('API health check failed: $e');
      return false;
    }
  }

  // Get user by email
  static Future<Map<String, dynamic>?> getUserByEmail(String email) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/users/email/$email'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return data['user'];
        }
      }

      return null;
    } catch (e) {
      print('Error getting user by email: $e');
      return null;
    }
  }

  // Create social user
  static Future<ApiUserCredential?> createSocialUser({
    required String email,
    required String name,
    required String provider,
    required String providerId,
    String? photoUrl,
    String role = 'customer',
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/social'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'name': name,
          'provider': provider,
          'provider_id': providerId,
          'photo_url': photoUrl,
          'role': role,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 201 && data['success'] == true) {
        // Get full user data
        final fullUserData = await getUserData(data['user']['id']);
        if (fullUserData != null) {
          _currentUser = ApiUser.fromMap(fullUserData);
          await _saveCurrentUser(_currentUser!);
          _authStateController.add(_currentUser);
          return ApiUserCredential(
              user: _currentUser, isNewUser: data['is_new_user'] ?? true);
        }
      } else {
        throw Exception(data['error'] ?? 'Social user creation failed');
      }

      return null;
    } catch (e) {
      throw Exception('Social user creation failed: ${e.toString()}');
    }
  }

  // Get all users (for testing)
  static Future<List<Map<String, dynamic>>> getAllUsers() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/users'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(data['users']);
        }
      }

      return [];
    } catch (e) {
      print('Error getting all users: $e');
      return [];
    }
  }

  // Private helper methods
  static Future<void> _saveCurrentUser(ApiUser user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, jsonEncode(user.toMap()));
  }

  // Dispose resources
  static void dispose() {
    _authStateController.close();
  }
}

// API user classes
class ApiUser {
  final String id;
  final String email;
  final String name;
  final int? age;
  final String? bio;
  final String? location;
  final List<String> profileImages;
  final List<String> interests;
  final String? gender;
  final String? lookingFor;
  final int coins;
  final bool isPremium;
  final String role;
  final DateTime createdAt;
  final DateTime updatedAt;

  ApiUser({
    required this.id,
    required this.email,
    required this.name,
    this.age,
    this.bio,
    this.location,
    this.profileImages = const [],
    this.interests = const [],
    this.gender,
    this.lookingFor,
    this.coins = 0,
    this.isPremium = false,
    this.role = 'customer',
    required this.createdAt,
    required this.updatedAt,
  });

  factory ApiUser.fromMap(Map<String, dynamic> map) {
    return ApiUser(
      id: map['id'].toString(),
      email: map['email'] ?? '',
      name: map['name'] ?? '',
      age: map['age'],
      bio: map['bio'],
      location: map['location'],
      profileImages: map['profile_images'] != null
          ? List<String>.from(map['profile_images'])
          : [],
      interests:
          map['interests'] != null ? List<String>.from(map['interests']) : [],
      gender: map['gender'],
      lookingFor: map['looking_for'],
      coins: map['coins'] ?? 0,
      isPremium: map['is_premium'] ?? false,
      role: map['role'] ?? 'customer',
      createdAt:
          DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt:
          DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'firebase_uid': id, // Use the same ID as firebase_uid for mock data
      'email': email,
      'name': name,
      'age': age,
      'bio': bio,
      'location': location,
      'profile_images': profileImages,
      'interests': interests,
      'gender': gender,
      'looking_for': lookingFor,
      'coins': coins,
      'is_premium': isPremium,
      'role': role,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class ApiUserCredential {
  final ApiUser? user;
  final bool isNewUser;

  ApiUserCredential({
    this.user,
    this.isNewUser = false,
  });
}
