import 'dart:convert';
import 'dart:math';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'dart:io';

class DeviceQuickLoginService {
  static const String _deviceQuickLoginKey = 'device_quick_login_account';
  static const String _deviceIdKey = 'device_unique_id';

  // Random name components for generating names
  static const List<String> _adjectives = [
    'Cool',
    'Smart',
    'Happy',
    'Bright',
    'Swift',
    'Bold',
    'Kind',
    'Wise',
    'Calm',
    'Wild',
    'Free',
    'Pure',
    'True',
    'Fast',
    'Warm',
    'Nice',
    'Fun',
    'Epic',
    'Cute',
    'Zen',
    'Rad',
    'Hip',
    'Fly',
    'Ace'
  ];

  static const List<String> _nouns = [
    'Tiger',
    'Eagle',
    'Wolf',
    'Lion',
    'Bear',
    'Fox',
    'Hawk',
    'Star',
    'Moon',
    'Sun',
    'Fire',
    'Wave',
    'Rock',
    'Wind',
    'Sky',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    'Gem',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>'
  ];

  // Get unique device identifier
  static Future<String> getDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    String? deviceId = prefs.getString(_deviceIdKey);

    if (deviceId != null) {
      return deviceId;
    }

    // Generate device ID based on device info
    final deviceInfo = DeviceInfoPlugin();
    String identifier = '';

    try {
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        identifier =
            '${androidInfo.model}_${androidInfo.id}_${androidInfo.fingerprint}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        identifier =
            '${iosInfo.model}_${iosInfo.identifierForVendor}_${iosInfo.systemVersion}';
      } else {
        // Fallback for other platforms
        identifier = 'unknown_${DateTime.now().millisecondsSinceEpoch}';
      }

      // Create a hash of the identifier for privacy
      final bytes = utf8.encode(identifier);
      final digest = sha256.convert(bytes);
      deviceId = digest.toString().substring(0, 16);

      // Save for future use
      await prefs.setString(_deviceIdKey, deviceId);
      return deviceId;
    } catch (e) {
      // Fallback to timestamp-based ID
      deviceId = 'device_${DateTime.now().millisecondsSinceEpoch}';
      await prefs.setString(_deviceIdKey, deviceId);
      return deviceId;
    }
  }

  // Generate random name with numbers
  static String generateRandomName() {
    final random = Random();
    final adjective = _adjectives[random.nextInt(_adjectives.length)];
    final noun = _nouns[random.nextInt(_nouns.length)];
    final number = random.nextInt(999) + 1; // 1-999

    return '$adjective$noun$number';
  }

  // Check if device already has a quick login account
  static Future<Map<String, dynamic>?> getExistingQuickLoginAccount() async {
    final prefs = await SharedPreferences.getInstance();
    final accountJson = prefs.getString(_deviceQuickLoginKey);

    if (accountJson != null) {
      try {
        return jsonDecode(accountJson);
      } catch (e) {
        // If corrupted, remove it
        await prefs.remove(_deviceQuickLoginKey);
        return null;
      }
    }

    return null;
  }

  // Save quick login account for this device
  static Future<void> saveQuickLoginAccount(
      Map<String, dynamic> accountData) async {
    final prefs = await SharedPreferences.getInstance();
    final deviceId = await getDeviceId();

    // Add device ID to account data
    accountData['device_id'] = deviceId;
    accountData['created_at'] = DateTime.now().toIso8601String();
    accountData['last_login'] = DateTime.now().toIso8601String();

    await prefs.setString(_deviceQuickLoginKey, jsonEncode(accountData));
  }

  // Update last login time
  static Future<void> updateLastLogin() async {
    final existingAccount = await getExistingQuickLoginAccount();
    if (existingAccount != null) {
      existingAccount['last_login'] = DateTime.now().toIso8601String();
      await saveQuickLoginAccount(existingAccount);
    }
  }

  // Clear quick login account (for logout)
  static Future<void> clearQuickLoginAccount() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_deviceQuickLoginKey);
  }

  // Check if account is valid (not too old, etc.)
  static bool isAccountValid(Map<String, dynamic> account) {
    try {
      final createdAt = DateTime.parse(account['created_at']);
      final now = DateTime.now();
      final daysSinceCreation = now.difference(createdAt).inDays;

      // Account is valid for 30 days
      return daysSinceCreation <= 30;
    } catch (e) {
      return false;
    }
  }

  // Get or create quick login account for this device
  static Future<Map<String, dynamic>> getOrCreateQuickLoginAccount() async {
    // Check for existing account
    final existingAccount = await getExistingQuickLoginAccount();

    if (existingAccount != null && isAccountValid(existingAccount)) {
      // Update last login and return existing account
      await updateLastLogin();
      return existingAccount;
    }

    // Create new account
    final deviceId = await getDeviceId();
    final randomName = generateRandomName();

    final newAccount = {
      'id': 'quick_${deviceId}_${DateTime.now().millisecondsSinceEpoch}',
      'name': randomName,
      'email': 'quick_$<EMAIL>',
      'age': 18,
      'gender': 'Male', // Default gender
      'coins': 100, // Starting coins
      'device_id': deviceId,
      'is_quick_login': true,
      'created_at': DateTime.now().toIso8601String(),
      'last_login': DateTime.now().toIso8601String(),
    };

    await saveQuickLoginAccount(newAccount);
    return newAccount;
  }

  // Sync coins with stored account
  static Future<void> syncCoins(int newCoinBalance) async {
    final existingAccount = await getExistingQuickLoginAccount();
    if (existingAccount != null) {
      existingAccount['coins'] = newCoinBalance;
      existingAccount['last_updated'] = DateTime.now().toIso8601String();
      await saveQuickLoginAccount(existingAccount);
    }
  }

  // Get stored coin balance
  static Future<int> getStoredCoinBalance() async {
    final existingAccount = await getExistingQuickLoginAccount();
    if (existingAccount != null) {
      return existingAccount['coins'] ?? 100;
    }
    return 100; // Default coins
  }
}
