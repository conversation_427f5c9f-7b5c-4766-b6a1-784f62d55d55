import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart'
    if (dart.library.html) 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import '../services/api_auth_service.dart';
import '../services/social_auth_service.dart';
import '../services/coin_service.dart';

enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

class DatabaseAuthProvider extends ChangeNotifier {
  AuthStatus _status = AuthStatus.initial;
  UserModel? _user;
  String? _errorMessage;
  ApiUser? _apiUser;
  final SocialAuthService _socialAuthService = SocialAuthService();
  final CoinService _coinService = CoinService();

  // Getters
  AuthStatus get status => _status;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  ApiUser? get apiUser => _apiUser;
  bool get isAuthenticated =>
      _status == AuthStatus.authenticated && _user != null;
  bool get isLoading => _status == AuthStatus.loading;

  DatabaseAuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    _initializeApiAuth();
  }

  Future<void> _initializeApiAuth() async {
    try {
      // Initialize API auth service
      await ApiAuthService.initialize();

      // Listen to API auth state changes
      ApiAuthService.authStateChanges.listen((ApiUser? apiUser) {
        _apiUser = apiUser;
        if (apiUser != null) {
          _loadUserData(apiUser.id);
        } else {
          _setUnauthenticated();
        }
      });
    } catch (e) {
      debugPrint('Error initializing API auth: $e');
      _setUnauthenticated();
    }
  }

  Future<void> _loadUserData(String userId) async {
    try {
      _setStatus(AuthStatus.loading);

      debugPrint('🔄 Loading user data for ID: $userId');
      final userData = await ApiAuthService.getUserData(userId);

      if (userData != null) {
        debugPrint('✅ User data loaded: ${userData.keys}');
        debugPrint('📋 User data content: $userData');

        // Create UserModel from the data
        _user = UserModel.fromMap(userData);

        // Initialize coins for new users
        await _coinService.initializeNewUser();

        _setStatus(AuthStatus.authenticated);
        debugPrint(
            '🎉 User authenticated successfully: ${_user!.email} (ID: ${_user!.id})');
      } else {
        debugPrint('❌ No user data found for ID: $userId');

        // If we have an API user but no detailed data, create a basic UserModel
        if (_apiUser != null && _apiUser!.id == userId) {
          debugPrint('🔄 Creating UserModel from ApiUser data');
          _user = UserModel(
            id: _apiUser!.id,
            firebaseUid:
                _apiUser!.id, // Use the same ID as firebaseUid for mock data
            email: _apiUser!.email,
            name: _apiUser!.name,
            age: _apiUser!.age,
            gender: _apiUser!.gender,
            coins: _apiUser!.coins,
            isPremium: _apiUser!.isPremium,
            createdAt: _apiUser!.createdAt,
            updatedAt: _apiUser!.updatedAt,
          );

          // Initialize coins for new users
          await _coinService.initializeNewUser();

          _setStatus(AuthStatus.authenticated);
          debugPrint(
              '🎉 User authenticated from ApiUser: ${_user!.email} (ID: ${_user!.id})');
        } else {
          _setError('User data not found');
        }
      }
    } catch (e) {
      debugPrint('💥 Error loading user data: $e');
      _setError('Failed to load user data: ${e.toString()}');
    }
  }

  // Sign up with email and password
  Future<bool> signUp({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      _setStatus(AuthStatus.loading);
      _clearError();

      final credential = await ApiAuthService.signUpWithEmail(
        email: email,
        password: password,
        name: name,
      );

      if (credential?.user != null) {
        // Directly load user data and set authenticated state
        _apiUser = credential!.user;
        await _loadUserData(credential.user!.id);
        return true;
      } else {
        _setError('Failed to create account');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Quick Login - Device-specific with automatic random name generation
  Future<bool> quickLogin({
    String? name, // Optional - will generate random if not provided
    String? gender, // Optional - will use default if not provided
    int? age, // Optional - will use default if not provided
  }) async {
    try {
      _setStatus(AuthStatus.loading);
      _clearError();

      // Use API service for device-specific quick login
      final credential = await ApiAuthService.quickLogin(
        name: name, // Can be null - service will generate random name
        gender: gender, // Can be null - service will use default
        age: age, // Can be null - service will use default
      );

      if (credential?.user != null) {
        // Directly load user data and set authenticated state
        _apiUser = credential!.user;
        await _loadUserData(credential.user!.id);
        return true;
      } else {
        _setError('Failed to access quick login account');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Sign up with detailed information
  Future<bool> signUpWithDetails({
    required String email,
    required String password,
    required String name,
    int? age,
    String? gender,
  }) async {
    try {
      _setStatus(AuthStatus.loading);
      _clearError();

      // Use API service to connect to backend
      final credential = await ApiAuthService.signUpWithDetails(
        email: email,
        password: password,
        name: name,
        age: age,
        gender: gender,
      );

      if (credential?.user != null) {
        // Directly load user data and set authenticated state
        _apiUser = credential!.user;
        await _loadUserData(credential.user!.id);
        return true;
      } else {
        _setError('Failed to create account');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Sign in with email and password
  Future<bool> signIn({
    required String email,
    required String password,
    String role = 'customer',
  }) async {
    try {
      _setStatus(AuthStatus.loading);
      _clearError();

      // Use API service to connect to backend
      final credential = await ApiAuthService.signInWithEmail(
        email: email,
        password: password,
      );

      if (credential?.user != null) {
        // Directly load user data and set authenticated state
        _apiUser = credential!.user;
        await _loadUserData(credential.user!.id);
        return true;
      } else {
        _setError('Failed to sign in');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Sign in with Google
  Future<bool> signInWithGoogle({String role = 'customer'}) async {
    try {
      _setStatus(AuthStatus.loading);
      _clearError();

      debugPrint('🔵 Starting Google Sign In...');

      // Sign in with Google using Firebase
      final userCredential = await _socialAuthService.signInWithGoogle();

      if (userCredential?.user != null) {
        final firebaseUser = userCredential!.user!;
        debugPrint('✅ Google Sign In Success: ${firebaseUser.email}');

        // Create or get user in our database
        final success = await _createOrGetSocialUser(
          firebaseUser: firebaseUser,
          provider: 'google',
          role: role,
        );

        return success;
      } else {
        _setError('Google sign-in was cancelled');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Google Sign In Error: $e');
      _setError(_socialAuthService.getErrorMessage(e));
      return false;
    }
  }

  // Sign in with Facebook
  Future<bool> signInWithFacebook() async {
    try {
      _setStatus(AuthStatus.loading);
      _clearError();

      debugPrint('🔵 Starting Facebook Sign In...');

      // Sign in with Facebook using Firebase
      final userCredential = await _socialAuthService.signInWithFacebook();

      if (userCredential?.user != null) {
        final firebaseUser = userCredential!.user!;
        debugPrint('✅ Facebook Sign In Success: ${firebaseUser.email}');

        // Create or get user in our database
        final success = await _createOrGetSocialUser(
          firebaseUser: firebaseUser,
          provider: 'facebook',
        );

        return success;
      } else {
        _setError('Facebook sign-in was cancelled');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Facebook Sign In Error: $e');
      _setError(_socialAuthService.getErrorMessage(e));
      return false;
    }
  }

  // Create or get social user in our database
  Future<bool> _createOrGetSocialUser({
    required User firebaseUser,
    required String provider,
    String role = 'customer',
  }) async {
    try {
      // Check if user already exists in our database by email
      final existingUser =
          await ApiAuthService.getUserByEmail(firebaseUser.email!);

      if (existingUser != null) {
        // User exists, sign them in
        debugPrint('📱 Existing user found, signing in...');
        _apiUser = ApiUser(
          id: existingUser['id'].toString(),
          email: existingUser['email'],
          name: existingUser['name'],
          createdAt: DateTime.parse(
              existingUser['created_at'] ?? DateTime.now().toIso8601String()),
          updatedAt: DateTime.parse(
              existingUser['updated_at'] ?? DateTime.now().toIso8601String()),
        );
        await _loadUserData(existingUser['id']);
        return true;
      } else {
        // Create new user in our database
        debugPrint('📱 Creating new social user...');
        final credential = await ApiAuthService.createSocialUser(
          email: firebaseUser.email!,
          name: firebaseUser.displayName ?? 'User',
          provider: provider,
          providerId: firebaseUser.uid,
          photoUrl: firebaseUser.photoURL,
          role: role,
        );

        if (credential?.user != null) {
          _apiUser = credential!.user;
          await _loadUserData(credential.user!.id);
          return true;
        } else {
          _setError('Failed to create user account');
          return false;
        }
      }
    } catch (e) {
      debugPrint('❌ Create/Get Social User Error: $e');
      _setError('Failed to process social login: ${e.toString()}');
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      _setStatus(AuthStatus.loading);
      await ApiAuthService.signOut();
      _setUnauthenticated();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // Reset password
  Future<bool> resetPassword(String email) async {
    try {
      _clearError();
      await ApiAuthService.resetPassword(email);
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Update user profile
  Future<bool> updateProfile({
    String? name,
    int? age,
    String? bio,
    String? location,
    List<String>? interests,
    String? gender,
    String? lookingFor,
  }) async {
    if (_user == null) return false;

    try {
      _setStatus(AuthStatus.loading);

      final success = await ApiAuthService.updateUserProfile(
        userId: _user!.id,
        name: name,
        age: age,
        bio: bio,
        location: location,
        interests: interests,
        gender: gender,
        lookingFor: lookingFor,
      );

      if (success) {
        // Reload user data to get updated information
        await _loadUserData(_user!.id);
        return true;
      } else {
        _setError('Failed to update profile');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Delete account
  Future<bool> deleteAccount() async {
    try {
      _setStatus(AuthStatus.loading);
      await ApiAuthService.deleteAccount();
      _setUnauthenticated();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Refresh user data
  Future<void> refreshUserData() async {
    if (_apiUser != null) {
      await _loadUserData(_apiUser!.id);
    }
  }

  // Helper methods
  void _setStatus(AuthStatus status) {
    _status = status;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _status = AuthStatus.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  void _setUnauthenticated() {
    _status = AuthStatus.unauthenticated;
    _user = null;
    _apiUser = null;
    _errorMessage = null;
    notifyListeners();
  }

  // Clear error message
  void clearError() {
    _clearError();
    notifyListeners();
  }

  // Check if user has complete profile
  bool get hasCompleteProfile {
    return _user?.hasCompleteProfile ?? false;
  }

  // Get user's coin balance
  int get coinBalance {
    return _user?.coins ?? 0;
  }

  // Get coin balance from coin service
  Future<int> getCoinBalance() async {
    return await _coinService.getCoinBalance();
  }

  // Update coin balance (for local state, should sync with backend)
  void updateCoins(int newBalance) {
    if (_user != null) {
      _user = _user!.copyWith(coins: newBalance);
      notifyListeners();
    }
  }

  // Get user email for display
  String get userEmail {
    return _user?.email ?? _apiUser?.email ?? '';
  }

  // Get user name for display
  String get userName {
    return _user?.name ?? _apiUser?.name ?? '';
  }

  // Logout user
  Future<void> logout() async {
    try {
      await ApiAuthService.signOut();
      _setUnauthenticated();
    } catch (e) {
      _setError('Logout failed: ${e.toString()}');
    }
  }
}
