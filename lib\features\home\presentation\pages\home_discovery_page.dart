import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/providers/database_auth_provider.dart';
import '../widgets/action_buttons.dart';

class HomeDiscoveryPage extends StatefulWidget {
  const HomeDiscoveryPage({super.key});

  @override
  State<HomeDiscoveryPage> createState() => _HomeDiscoveryPageState();
}

class _HomeDiscoveryPageState extends State<HomeDiscoveryPage>
    with TickerProviderStateMixin {
  late AnimationController _cardController;
  late AnimationController _buttonController;
  late Animation<double> _cardAnimation;
  late Animation<double> _buttonAnimation;

  List<Map<String, dynamic>> _profiles = [];
  int _currentIndex = 0;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadProfiles();
  }

  void _setupAnimations() {
    _cardController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _cardAnimation = CurvedAnimation(
      parent: _cardController,
      curve: Curves.easeInOut,
    );
    _buttonAnimation = CurvedAnimation(
      parent: _buttonController,
      curve: Curves.easeInOut,
    );

    _cardController.forward();
    _buttonController.forward();
  }

  Future<void> _loadProfiles() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Get demo profiles for now
      // In production, this would fetch from your API
      final demoProfiles = _generateDemoProfiles();

      setState(() {
        _profiles = demoProfiles;
        _currentIndex = 0;
        _isLoading = false;
      });

      _cardController.forward();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> _generateDemoProfiles() {
    return [
      {
        'id': '1',
        'name': 'Emma',
        'age': 25,
        'bio': 'Love hiking and coffee ☕️',
        'images': [
          'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=600&fit=crop&crop=face'
        ],
        'distance': 2.5,
        'interests': ['Hiking', 'Coffee', 'Photography'],
        'isOnline': true,
      },
      {
        'id': '2',
        'name': 'Sarah',
        'age': 28,
        'bio': 'Yoga instructor & dog lover 🐕',
        'images': [
          'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=600&fit=crop&crop=face'
        ],
        'distance': 5.2,
        'interests': ['Yoga', 'Dogs', 'Meditation'],
        'isOnline': false,
      },
      {
        'id': '3',
        'name': 'Jessica',
        'age': 24,
        'bio': 'Artist and traveler ✈️',
        'images': [
          'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=600&fit=crop&crop=face'
        ],
        'distance': 3.8,
        'interests': ['Art', 'Travel', 'Music'],
        'isOnline': true,
      },
      {
        'id': '4',
        'name': 'Amanda',
        'age': 26,
        'bio': 'Foodie & adventure seeker 🍕',
        'images': [
          'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=600&fit=crop&crop=face'
        ],
        'distance': 1.2,
        'interests': ['Food', 'Adventure', 'Cooking'],
        'isOnline': true,
      },
      {
        'id': '5',
        'name': 'Lisa',
        'age': 27,
        'bio': 'Book lover & wine enthusiast 📚🍷',
        'images': [
          'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=400&h=600&fit=crop&crop=face'
        ],
        'distance': 4.5,
        'interests': ['Reading', 'Wine', 'Writing'],
        'isOnline': false,
      },
    ];
  }

  Future<void> _handleSwipe(SwipeDirection direction) async {
    if (_currentIndex >= _profiles.length) return;

    final currentProfile = _profiles[_currentIndex];

    try {
      // Animate card out
      await _cardController.reverse();

      // Record the swipe action (in production, save to database)
      debugPrint('Swiped ${direction.name} on ${currentProfile['name']}');

      // Check for match if it was a like
      if (direction == SwipeDirection.right) {
        // Simulate match chance (30%)
        if (DateTime.now().millisecond % 10 < 3) {
          _showMatchDialog(currentProfile);
        }
      }

      // Move to next profile
      setState(() {
        _currentIndex++;
      });

      // Animate new card in or load more profiles
      if (_currentIndex < _profiles.length) {
        _cardController.forward();
      } else {
        _loadMoreProfiles();
      }
    } catch (e) {
      _showError('Failed to process swipe: ${e.toString()}');
    }
  }

  Future<void> _loadMoreProfiles() async {
    // In production, load more profiles from API
    await Future.delayed(const Duration(seconds: 1));

    final moreProfiles = _generateDemoProfiles();
    setState(() {
      _profiles.addAll(moreProfiles);
    });

    _cardController.forward();
  }

  void _showMatchDialog(Map<String, dynamic> profile) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => MatchDialog(profile: profile),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showFullImage(String imageUrl) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(
                child: Image.network(
                  imageUrl,
                  fit: BoxFit.contain,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return const Center(
                      child: CircularProgressIndicator(
                        color: Color(0xFFFF6B9D),
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    return const Center(
                      child: Icon(
                        Icons.error,
                        color: Colors.white,
                        size: 64,
                      ),
                    );
                  },
                ),
              ),
            ),
            Positioned(
              top: 40,
              right: 20,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _requestAudioCall(Map<String, dynamic> profile) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Requesting audio call with ${profile['name']}...'),
        backgroundColor: const Color(0xFFFF6B9D),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _requestVideoCall(Map<String, dynamic> profile) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Requesting video call with ${profile['name']}...'),
        backgroundColor: const Color(0xFFFF6B9D),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _sendMessage(Map<String, dynamic> profile) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening chat with ${profile['name']}...'),
        backgroundColor: const Color(0xFFFF6B9D),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  void dispose() {
    _cardController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1F2937), // Dark blue-grey background
      body: Container(
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(),
              Expanded(
                child: _isLoading
                    ? _buildLoadingState()
                    : _error != null
                        ? _buildErrorState()
                        : _currentIndex >= _profiles.length
                            ? _buildNoMoreProfilesState()
                            : _buildSwipeArea(),
              ),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Consumer<DatabaseAuthProvider>(
      builder: (context, authProvider, child) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Discover',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Hello, ${authProvider.user?.name ?? 'User'}!',
                    style: GoogleFonts.poppins(
                      color: Colors.white70,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: () {
                      // TODO: Open filters
                    },
                    icon: const Icon(
                      Icons.tune,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      // TODO: Open notifications
                    },
                    icon: const Icon(
                      Icons.notifications_outlined,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.white),
          SizedBox(height: 16),
          Text(
            'Finding amazing people...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.white,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'Oops! Something went wrong',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error',
            style: GoogleFonts.poppins(
              color: Colors.white70,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadProfiles,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: const Color(0xFFFF6B9D),
            ),
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  Widget _buildNoMoreProfilesState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.favorite,
            color: Colors.white,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'No more profiles!',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Check back later for new people',
            style: GoogleFonts.poppins(
              color: Colors.white70,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadProfiles,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: const Color(0xFFFF6B9D),
            ),
            child: const Text('Refresh'),
          ),
        ],
      ),
    );
  }

  Widget _buildSwipeArea() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: AnimatedBuilder(
          animation: _cardAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _cardAnimation.value,
              child: Opacity(
                opacity: _cardAnimation.value,
                child: _buildProfileCard(_profiles[_currentIndex]),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfileCard(Map<String, dynamic> profile) {
    return GestureDetector(
      onPanUpdate: (details) {
        // Handle swipe gestures
        if (details.delta.dx > 10) {
          _handleSwipe(SwipeDirection.right);
        } else if (details.delta.dx < -10) {
          _handleSwipe(SwipeDirection.left);
        } else if (details.delta.dy < -10) {
          _handleSwipe(SwipeDirection.up);
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(
            horizontal: 4, vertical: 8), // Reduced margin for wider card
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Stack(
            children: [
              // Background Image - Tappable for full view
              GestureDetector(
                onTap: () => _showFullImage(profile['images'][0]),
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: NetworkImage(profile['images'][0]),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),

              // Minimalist gradient overlay
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.6),
                    ],
                  ),
                ),
              ),

              // Minimalist Profile Info
              Positioned(
                bottom: 16,
                left: 16,
                right: 16,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name, Age and Online Status
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            '${profile['name']}, ${profile['age']}',
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        if (profile['isOnline'])
                          Container(
                            width: 10,
                            height: 10,
                            decoration: const BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // Action buttons under age and gender
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildActionButton(
                          icon: Icons.phone,
                          color: const Color(0xFFFF6B9D),
                          onTap: () => _requestAudioCall(profile),
                        ),
                        _buildActionButton(
                          icon: Icons.videocam,
                          color: const Color(0xFFFF6B9D),
                          onTap: () => _requestVideoCall(profile),
                        ),
                        _buildActionButton(
                          icon: Icons.message,
                          color: const Color(0xFFFF6B9D),
                          onTap: () => _sendMessage(profile),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: color,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return AnimatedBuilder(
      animation: _buttonAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _buttonAnimation.value,
          child: ActionButtons(
            onPass: () => _handleSwipe(SwipeDirection.left),
            onSuperLike: () => _handleSwipe(SwipeDirection.up),
            onLike: () => _handleSwipe(SwipeDirection.right),
          ),
        );
      },
    );
  }
}

enum SwipeDirection { left, right, up }

// Match Dialog Widget
class MatchDialog extends StatelessWidget {
  final Map<String, dynamic> profile;

  const MatchDialog({super.key, required this.profile});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFFE8B4CB), Color(0xFFB8A9C9)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.favorite,
              color: Colors.white,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'It\'s a Match!',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'You and ${profile['name']} liked each other',
              style: GoogleFonts.poppins(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.white),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Keep Swiping'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // TODO: Navigate to chat
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: const Color(0xFFFF6B9D),
                    ),
                    child: const Text('Say Hello'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
